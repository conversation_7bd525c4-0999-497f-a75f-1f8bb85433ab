package com.sbpay

import androidx.room.*

@Dao
interface PendingSmsMessageDao {
    @Insert
    fun insert(message: PendingSmsMessage): Long

    @Query("SELECT * FROM pending_sms_messages WHERE retryCount < 300 AND (lastRetryTimestamp + 300000) < :currentTime ORDER BY timestamp ASC")
    fun getMessagesForRetry(currentTime: Long): List<PendingSmsMessage>

    @Update
    fun update(message: PendingSmsMessage): Int

    @Delete
    fun delete(message: PendingSmsMessage): Int

    @Query("DELETE FROM pending_sms_messages WHERE id = :messageId")
    fun deleteById(messageId: Long): Int
}