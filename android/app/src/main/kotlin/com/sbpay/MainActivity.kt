package com.sbpay

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import android.telephony.SubscriptionInfo
import android.telephony.SubscriptionManager
import android.telephony.TelephonyManager
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.tencent.mmkv.MMKV
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodChannel
import com.sbpay.BootCompletedReceiver

class MainActivity: FlutterActivity() {
    companion object {
        private const val TAG = "MainActivity"
        private const val CHANNEL = "com.sbpay/native"
        private const val REQUEST_CODE_BATTERY_OPTIMIZATION = 1001
    }
    
    private lateinit var methodChannel: MethodChannel
    private lateinit var eventChannel: EventChannel
    private var simStateMonitor: SimStateMonitorModule? = null
    private lateinit var smsModule: SmsModule
    
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        // Initialize MMKV first
        MMKV.initialize(this)
        
        methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
        eventChannel = EventChannel(flutterEngine.dartExecutor.binaryMessenger, "$CHANNEL/events")
        
        // Set method channel references for services
        SmsListenerService.methodChannel = methodChannel
        
        // Initialize SmsModule
        smsModule = SmsModule(this)
        
        // Initialize SimStateMonitorModule
        simStateMonitor = SimStateMonitorModule(this)
        
        // Set up event channel stream handler
        eventChannel.setStreamHandler(object : EventChannel.StreamHandler {
            override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                // Store the events sink so services can use it
                SmsListenerService.eventsSink = events
                SimStateMonitorService.eventsSink = events
            }
            
            override fun onCancel(arguments: Any?) {
                // Clean up references
                SmsListenerService.eventsSink = null
                SimStateMonitorService.eventsSink = null
            }
        })
        
        methodChannel.setMethodCallHandler { call, result ->
            Log.d(TAG, "Method call received: ${call.method}")
            
            when (call.method) {
                "getSimInfo" -> {
                    try {
                        val simInfo = getSimInfo()
                        Log.d(TAG, "SIM info retrieved successfully")
                        result.success(simInfo)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error getting SIM info", e)
                        result.error("ERROR", "Error getting SIM info: ${e.message}", null)
                    }
                }
                "startSmsListener" -> {
                    val endpoint = call.argument<String>("endpoint")
                    val token = call.argument<String>("token")
                    val verifiedSims = call.argument<String>("verifiedSims")
                    
                    if (endpoint != null && token != null) {
                        val success = startSmsListener(endpoint, token, verifiedSims ?: "")
                        result.success(success)
                    } else {
                        result.error("INVALID_ARGS", "Missing required arguments", null)
                    }
                }
                "stopSmsListener" -> {
                    val success = stopSmsListener()
                    result.success(success)
                }
                "isListening" -> {
                    result.success(SmsListenerService.isServiceRunning)
                }
                "startSimStateMonitoring" -> {
                    val success = startSimStateMonitoring()
                    result.success(success)
                }
                "stopSimStateMonitoring" -> {
                    stopSimStateMonitoring()
                    result.success(true)
                }
                "isIgnoringBatteryOptimizations" -> {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        result.success(isIgnoringBatteryOptimizations())
                    } else {
                        result.success(true)
                    }
                }
                "requestIgnoreBatteryOptimizations" -> {
                    requestIgnoreBatteryOptimizations()
                    result.success(true)
                }
                "sendOtp" -> {
                    val phoneNumber = call.argument<String>("phoneNumber")
                    val simSlot = call.argument<Int>("simSlot")
                    
                    if (phoneNumber != null && simSlot != null) {
                        // For demo purposes, always return success
                        // In production, integrate with actual OTP service
                        result.success(true)
                    } else {
                        result.error("INVALID_ARGS", "Missing phone number or SIM slot", null)
                    }
                }
                "verifyOtp" -> {
                    val phoneNumber = call.argument<String>("phoneNumber")
                    val otp = call.argument<String>("otp")
                    val simSlot = call.argument<Int>("simSlot")
                    
                    if (phoneNumber != null && otp != null && simSlot != null) {
                        // For demo purposes, accept "123456" as valid OTP
                        val isValid = otp == "123456"
                        result.success(isValid)
                    } else {
                        result.error("INVALID_ARGS", "Missing required arguments", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
        
        // Initialize SIM state monitor
        simStateMonitor = SimStateMonitorModule(this)
    }
    
    private fun getSimInfo(): Map<String, Any?> {
        val subscriptionManager = getSystemService(Context.TELEPHONY_SUBSCRIPTION_SERVICE) as SubscriptionManager
        val telephonyManager = getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        
        val simCards = mutableListOf<Map<String, Any?>>()
        
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED) {
            try {
                val subscriptionInfos = subscriptionManager.activeSubscriptionInfoList
                subscriptionInfos?.forEach { subscriptionInfo ->
                    // Try to get phone number with multiple fallback approaches
                    var phoneNumber = ""
                    
                    // First try: Get from subscription info
                    try {
                        phoneNumber = subscriptionInfo.number?.trim() ?: ""
                    } catch (e: Exception) {
                        android.util.Log.w("MainActivity", "Failed to get phone number from subscription info: ${e.message}")
                    }
                    
                    // Second try: Use subscription-specific TelephonyManager for Android S and above
                    if (phoneNumber.isEmpty() && android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                        try {
                            val subscriptionSpecificTelephonyManager = telephonyManager.createForSubscriptionId(subscriptionInfo.subscriptionId)
                            phoneNumber = subscriptionSpecificTelephonyManager.line1Number?.trim() ?: ""
                        } catch (e: Exception) {
                            android.util.Log.w("MainActivity", "Failed to get phone number from subscription-specific TelephonyManager: ${e.message}")
                        }
                    }
                    
                    // Third try: Use general TelephonyManager
                    if (phoneNumber.isEmpty()) {
                        try {
                            phoneNumber = telephonyManager.line1Number?.trim() ?: ""
                        } catch (e: Exception) {
                            android.util.Log.w("MainActivity", "Failed to get phone number from general TelephonyManager: ${e.message}")
                        }
                    }
                    
                    // Log if phone number is still empty
                    if (phoneNumber.isEmpty()) {
                        android.util.Log.w("MainActivity", "Phone number is empty for subscriptionId: ${subscriptionInfo.subscriptionId}")
                    } else {
                        android.util.Log.d("MainActivity", "Successfully retrieved phone number for subscriptionId ${subscriptionInfo.subscriptionId}: $phoneNumber")
                    }
                    
                    val simCard = mapOf<String, Any?>(
                        "slotIndex" to subscriptionInfo.simSlotIndex,
                        "carrierName" to (subscriptionInfo.carrierName?.toString() ?: "Unknown"),
                        "displayName" to (subscriptionInfo.displayName?.toString() ?: "Unknown"),
                        "iccId" to (subscriptionInfo.iccId ?: ""),
                        "countryIso" to (subscriptionInfo.countryIso ?: ""),
                        "isEmbedded" to subscriptionInfo.isEmbedded,
                        "subscriptionId" to subscriptionInfo.subscriptionId,
                        "phoneNumber" to phoneNumber
                    )
                    simCards.add(simCard)
                }
            } catch (e: Exception) {
                android.util.Log.e("MainActivity", "Error getting SIM info: ${e.message}", e)
            }
        }
        
        return mapOf(
            "simCards" to simCards.toList(),
            "activeSimCount" to simCards.size,
            "phoneCount" to telephonyManager.phoneCount
        )
    }

    private fun startSmsListener(endpoint: String, token: String, verifiedSims: String): Boolean {
        return try {
            val intent = Intent(this, SmsListenerService::class.java).apply {
                putExtra("api_endpoint", endpoint)
                putExtra("api_token", token)
                putExtra("verified_sims", verifiedSims)
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(intent)
            } else {
                startService(intent)
            }
            
            Log.d(TAG, "SMS listener service started with endpoint: $endpoint, verified SIMs: $verifiedSims")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error starting SMS listener service", e)
            false
        }
    }

    private fun stopSmsListener(): Boolean {
        return try {
            val intent = Intent(this, SmsListenerService::class.java)
            stopService(intent)
            Log.d(TAG, "SMS listener service stopped")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping SMS listener service", e)
            false
        }
    }

    private fun startSimStateMonitoring(): Boolean {
        return try {
            val success = simStateMonitor?.startMonitoring() ?: false
            Log.d(TAG, "SIM state monitoring started: $success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error starting SIM state monitoring", e)
            false
        }
    }

    private fun stopSimStateMonitoring() {
        try {
            simStateMonitor?.stopMonitoring()
            Log.d(TAG, "SIM state monitoring stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping SIM state monitoring", e)
        }
    }

    @RequiresApi(Build.VERSION_CODES.M)
    private fun isBatteryOptimizationEnabled(): Boolean {
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        return powerManager.isIgnoringBatteryOptimizations(getPackageName())
    }

    private fun requestBatteryOptimizationExemption() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            try {
                val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                    data = Uri.parse("package:${getPackageName()}")
                }
                startActivityForResult(intent, REQUEST_CODE_BATTERY_OPTIMIZATION)
            } catch (e: Exception) {
                Log.e(TAG, "Error requesting battery optimization exemption", e)
                // Fallback to general battery optimization settings
                try {
                    val fallbackIntent = Intent(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS)
                    startActivity(fallbackIntent)
                } catch (e2: Exception) {
                    Log.e(TAG, "Error opening battery optimization settings", e2)
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        stopSimStateMonitoring()
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (requestCode == REQUEST_CODE_BATTERY_OPTIMIZATION) {
            // Log that we received the result
            Log.d(TAG, "Received battery optimization request result")
            // The app will recheck permissions when it resumes
        }
    }
    
    private fun checkSimInfoPermission(): Boolean {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED
    }
    
    private fun isIgnoringBatteryOptimizations(): Boolean {
        val result = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            powerManager.isIgnoringBatteryOptimizations(packageName)
        } else {
            true // No battery optimization on older versions
        }
        Log.d(TAG, "isIgnoringBatteryOptimizations: $result")
        return result
    }
    
    private fun requestIgnoreBatteryOptimizations() {
        Log.d(TAG, "requestIgnoreBatteryOptimizations called")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
            intent.data = Uri.parse("package:${getPackageName()}")
            startActivity(intent)
            Log.d(TAG, "Battery optimization request intent started")
        } else {
            Log.d(TAG, "Battery optimization request not supported on this Android version")
        }
    }
}
