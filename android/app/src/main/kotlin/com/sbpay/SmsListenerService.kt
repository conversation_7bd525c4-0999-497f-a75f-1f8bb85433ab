package com.sbpay

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.os.IBinder
import android.os.PowerManager
import android.provider.Telephony
import android.telephony.SubscriptionManager
import androidx.core.app.NotificationCompat
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.EventChannel
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.io.IOException
import android.telephony.SmsMessage
import androidx.room.Room
import kotlinx.coroutines.*

class SmsListenerService : Service() {
    companion object {
        var methodChannel: MethodChannel? = null
        var eventsSink: EventChannel.EventSink? = null
        var isServiceRunning: Boolean = false
    }
    private val CHANNEL_ID = "sms_service_channel"
    private val NOTIFICATION_ID = 1
    private var smsReceiver: BroadcastReceiver? = null
    private var wakeLock: PowerManager.WakeLock? = null
    private var apiEndpoint: String? = null
    private var apiHeaders: Map<String, Any>? = null
    private val client = OkHttpClient()
    private val JSON = "application/json; charset=utf-8".toMediaType()
    private lateinit var database: AppDatabase
    private lateinit var pendingSmsMessageDao: PendingSmsMessageDao
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var retryJob: Job? = null

    private var networkCallback: ConnectivityManager.NetworkCallback? = null

    override fun onCreate() {
        super.onCreate()
        database = AppDatabase.getDatabase(this)
        pendingSmsMessageDao = database.pendingSmsMessageDao()
        createNotificationChannel()
        acquireWakeLock()
        startForeground(NOTIFICATION_ID, createNotification())
        registerSmsReceiver()
        registerNetworkCallback()
        startRetryJob()
    }

    override fun onDestroy() {
        android.util.Log.d("SmsListenerService", "Service onDestroy called - Cleaning up resources")
        coroutineScope.cancel()
        unregisterSmsReceiver()
        unregisterNetworkCallback()
        releaseWakeLock()
        super.onDestroy()
    }

    private suspend fun processMessages() {
        if (isProcessingMessages) {
            android.util.Log.d("SmsListenerService", "Messages already being processed, skipping")
            return
        }

        if (!isNetworkAvailable()) {
            android.util.Log.d("SmsListenerService", "Network not available, skipping message processing")
            return
        }

        isProcessingMessages = true
        try {
            val currentTime = System.currentTimeMillis()
            val pendingMessages = pendingSmsMessageDao.getMessagesForRetry(currentTime)
            android.util.Log.d("SmsListenerService", "Found ${pendingMessages.size} pending messages to process")

            if (pendingMessages.isNotEmpty()) {
                android.util.Log.d("SmsListenerService", "Starting to process pending messages in chunks")
                // Process messages in parallel with a maximum of 5 concurrent requests
                pendingMessages.chunked(5).forEachIndexed { index, chunk ->
                    android.util.Log.d("SmsListenerService", "Processing chunk ${index + 1} with ${chunk.size} messages")
                    coroutineScope {
                        val results = chunk.map { message ->
                            async {
                                android.util.Log.d("SmsListenerService", "Attempting to send message ID: ${message.id} to API")
                                val success = retryApiCall(message)
                                if (success) {
                                    pendingSmsMessageDao.deleteById(message.id)
                                    android.util.Log.d("SmsListenerService", "Successfully sent message ID: ${message.id} to API and deleted from local storage")
                                } else {
                                    val updatedMessage = message.copy(
                                        retryCount = message.retryCount + 1,
                                        lastRetryTimestamp = System.currentTimeMillis()
                                    )
                                    pendingSmsMessageDao.update(updatedMessage)
                                    android.util.Log.d("SmsListenerService", "Failed to send message ID: ${message.id}, updated retry count to: ${updatedMessage.retryCount}")
                                }
                                success
                            }
                        }.awaitAll()
                        
                        val successCount = results.count { it }
                        android.util.Log.d("SmsListenerService", "Chunk ${index + 1} completed: $successCount/${chunk.size} messages sent successfully")
                    }
                }
                android.util.Log.d("SmsListenerService", "Completed processing all pending messages")
            } else {
                android.util.Log.d("SmsListenerService", "No pending messages found to process")
            }
        } catch (e: Exception) {
            android.util.Log.e("SmsListenerService", "Error processing messages: ${e.message}")
            e.printStackTrace()
        } finally {
            isProcessingMessages = false
            android.util.Log.d("SmsListenerService", "Message processing completed, released processing lock")
        }
    }

    private fun registerNetworkCallback() {
        val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        networkCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: android.net.Network) {
                super.onAvailable(network)
                android.util.Log.d("SmsListenerService", "Network became available")
                // Validate network capabilities before processing
                val capabilities = connectivityManager.getNetworkCapabilities(network)
                if (capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true) {
                    android.util.Log.d("SmsListenerService", "Internet capability confirmed, processing messages")
                    coroutineScope.launch {
                        delay(2000) // Wait for network to stabilize
                        if (isNetworkAvailable()) { // Double-check network is still available
                            processMessages()
                        }
                    }
                }
            }

            override fun onLost(network: android.net.Network) {
                super.onLost(network)
                android.util.Log.d("SmsListenerService", "Network connection lost")
            }
        }

        val networkRequest = android.net.NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .addCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED) // Ensure network has valid Internet
            .build()

        try {
            connectivityManager.registerNetworkCallback(networkRequest, networkCallback!!)
            android.util.Log.d("SmsListenerService", "Network callback registered successfully")
        } catch (e: Exception) {
            android.util.Log.e("SmsListenerService", "Failed to register network callback: ${e.message}")
        }
    }

    private fun unregisterNetworkCallback() {
        networkCallback?.let { callback ->
            val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            try {
                connectivityManager.unregisterNetworkCallback(callback)
            } catch (e: Exception) {
                android.util.Log.e("SmsListenerService", "Error unregistering network callback: ${e.message}")
            }
        }
        networkCallback = null
    }

    private fun isNetworkAvailable(): Boolean {
        val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork
        val capabilities = connectivityManager.getNetworkCapabilities(network)
        return capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
    }

    private var isProcessingMessages = false

    private fun startRetryJob() {
        retryJob = coroutineScope.launch {
            while (isActive) {
                try {
                    if (!isNetworkAvailable()) {
                        android.util.Log.d("SmsListenerService", "Network not available, skipping scheduled retry")
                        delay(30000) // Wait for 30 seconds before next check
                        continue
                    }

                    processMessages()
                } catch (e: Exception) {
                    android.util.Log.e("SmsListenerService", "Error in retry job: ${e.message}")
                }
                delay(60000) // Wait for 1 minute before next retry attempt
            }
        }
    }

    private suspend fun retryApiCall(message: PendingSmsMessage, subscriptionId: Int = -1): Boolean {
        if (!isNetworkAvailable()) {
            android.util.Log.d("SmsListenerService", "Network not available for message ID: ${message.id}, skipping retry")
            return false
        }

        return try {
            val simNumber = getSimNumber(subscriptionId)
            android.util.Log.d("SmsListenerService", "Preparing API request for message ID: ${message.id} with SIM number: $simNumber")

            val messageContent = message.message?.replace("\n", " ")
            val jsonBody = JSONObject().apply {
                put("message", messageContent)
                put("payment_type", message.sender)
                put("receiver_number", message.receiverNumber ?: simNumber)
            }

            val endpoint = apiEndpoint
            if (endpoint == null) {
                android.util.Log.e("SmsListenerService", "API endpoint not configured for message ID: ${message.id}")
                return false
            }

            android.util.Log.d("SmsListenerService", "Sending request to API for message ID: ${message.id}\nEndpoint: $endpoint\nPayload: ${jsonBody}")

            val requestBody = jsonBody.toString().toRequestBody(JSON)
            val requestBuilder = Request.Builder()
                .url(endpoint)
                .post(requestBody)

            apiHeaders?.forEach { (key, value) ->
                requestBuilder.addHeader(key, value.toString())
                android.util.Log.d("SmsListenerService", "Added header - $key: $value")
            }

            val request = requestBuilder.build()
            client.newCall(request).execute().use { response -> 
                val responseCode = response.code
                val responseBody = response.body?.string()
                
                if (response.isSuccessful) {
                    android.util.Log.d("SmsListenerService", "Successfully sent message ID: ${message.id}\nResponse code: $responseCode\nResponse body: $responseBody")
                    true
                } else {
                    android.util.Log.e("SmsListenerService", "API request failed for message ID: ${message.id}\nResponse code: $responseCode\nResponse body: $responseBody")
                    false
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("SmsListenerService", "Exception while retrying message ID: ${message.id}\nError: ${e.message}\nStack trace: ${e.stackTrace.joinToString("\n")}")
            false
        }
    }

    private fun sendToApi(smsMessage: android.telephony.SmsMessage, subscriptionId: Int = -1) {
        val endpoint = apiEndpoint ?: return
        val simNumber = getSimNumber(subscriptionId)

        if (!isNetworkAvailable()) {
            android.util.Log.d("SmsListenerService", "Network not available, saving message for later retry")
            coroutineScope.launch {
                savePendingMessage(smsMessage, subscriptionId)
            }
            return
        }

        val jsonBody = JSONObject().apply {
            put("message", smsMessage.displayMessageBody)
            put("payment_type", smsMessage.displayOriginatingAddress)
            put("receiver_number", simNumber)
        }

        val requestBody = jsonBody.toString().toRequestBody(JSON)
        val requestBuilder = Request.Builder()
            .url(endpoint)
            .post(requestBody)

        apiHeaders?.forEach { (key, value) ->
            requestBuilder.addHeader(key, value.toString())
        }

        val request = requestBuilder.build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                android.util.Log.e("SmsListenerService", "API call failed: ${e.message}")
                coroutineScope.launch {
                    savePendingMessage(smsMessage, subscriptionId)
                }
            }

            override fun onResponse(call: Call, response: Response) {
                response.use { 
                    if (!response.isSuccessful) {
                        android.util.Log.e("SmsListenerService", "API returned error status: ${response.code}")
                        coroutineScope.launch {
                            savePendingMessage(smsMessage, subscriptionId)
                        }
                    } else {
                        android.util.Log.d("SmsListenerService", "Successfully sent SMS to API")
                    }
                }
            }
        })
    }

    private suspend fun savePendingMessage(smsMessage: android.telephony.SmsMessage, subscriptionId: Int = -1) {
        try {
            android.util.Log.d("SmsListenerService", "Preparing to save message from: ${smsMessage.displayOriginatingAddress} with subscriptionId: $subscriptionId")
            val simNumber = getSimNumber(subscriptionId)
            android.util.Log.d("SmsListenerService", "Using SIM number: $simNumber for message storage")
            
            val pendingMessage = PendingSmsMessage(
                sender = smsMessage.displayOriginatingAddress,
                message = smsMessage.displayMessageBody,
                timestamp = smsMessage.timestampMillis,
                paymentType = smsMessage.displayOriginatingAddress,
                receiverNumber = simNumber,
                retryCount = 0,
                lastRetryTimestamp = 0
            )
            pendingSmsMessageDao.insert(pendingMessage)
            
            // Verify message was saved by checking pending messages
            val pendingMessages = pendingSmsMessageDao.getMessagesForRetry(System.currentTimeMillis())
            android.util.Log.d("SmsListenerService", "Current pending messages count: ${pendingMessages.size}")
            
            if (!isNetworkAvailable()) {
                android.util.Log.d("SmsListenerService", "Message saved successfully. Will process when network becomes available")
            } else {
                android.util.Log.d("SmsListenerService", "Message saved and network available. Triggering immediate processing")
                processMessages()
            }
        } catch (e: Exception) {
            android.util.Log.e("SmsListenerService", "Error saving message to database", e)
            e.printStackTrace()
        }
    }

    private fun getSimNumber(subscriptionId: Int = -1): String {
        if (subscriptionId == -1) {
            android.util.Log.d("SmsListenerService", "No subscription ID provided")
            return "unknown"
        }

        android.util.Log.d("SmsListenerService", "Looking for SIM number for subscriptionId: $subscriptionId, total verified SIMs: ${verifiedSims.size}")
        
        val verifiedSim = verifiedSims.find { sim ->
            val simSubId = (sim["subscriptionId"] as? Number)?.toInt() ?: -1
            android.util.Log.d("SmsListenerService", "Checking SIM with subscriptionId: $simSubId")
            simSubId == subscriptionId
        }

        val phoneNumber = verifiedSim?.get("phone_number") as? String
        if (phoneNumber != null && phoneNumber.isNotEmpty()) {
            android.util.Log.d("SmsListenerService", "Using verified SIM number for subscriptionId $subscriptionId: $phoneNumber")
            return phoneNumber
        }
        
        if (verifiedSim != null) {
            android.util.Log.e("SmsListenerService", "Found SIM for subscriptionId $subscriptionId but phone_number is null/empty: '${verifiedSim["phone_number"]}'")
        } else {
            android.util.Log.e("SmsListenerService", "No verified SIM found for subscriptionId: $subscriptionId")
        }
        
        return "unknown"
    }
    private var verifiedSims: List<Map<String, Any>> = emptyList()

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (intent?.action == "STOP_SERVICE") {
            isServiceRunning = false
            stopSelf()
            return START_NOT_STICKY
        }
        
        isServiceRunning = true
        apiEndpoint = intent?.getStringExtra("api_endpoint")
        val token = intent?.getStringExtra("api_token")
        val verifiedSimsString = intent?.getStringExtra("verified_sims")
        
        // Set up API headers with token
        if (token != null) {
            apiHeaders = mapOf("Authorization" to "Bearer $token")
        }
        
        // Parse verified SIMs from JSON string
        verifiedSims = try {
            if (!verifiedSimsString.isNullOrEmpty()) {
                val jsonArray = org.json.JSONArray(verifiedSimsString)
                (0 until jsonArray.length()).map { i ->
                    val jsonObject = jsonArray.getJSONObject(i)
                    mapOf(
                        "subscriptionId" to jsonObject.optInt("subscriptionId", -1),
                        "phone_number" to jsonObject.optString("phone_number", ""),
                        "slotIndex" to jsonObject.optInt("slotIndex", -1),
                        "iccId" to jsonObject.optString("iccId", "")
                    )
                }
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            android.util.Log.e("SmsListenerService", "Error parsing verified SIMs: ${e.message}")
            emptyList()
        }
        
        // Log the parsed verified SIMs for debugging
        android.util.Log.d("SmsListenerService", "Parsed verified SIMs: ${verifiedSims.size}")
        verifiedSims.forEachIndexed { index, sim ->
            val subscriptionId = sim["subscriptionId"] as? Number
            val phoneNumber = sim["phone_number"] as? String
            val slotIndex = sim["slotIndex"] as? Number
            val iccId = sim["iccId"] as? String
            
            android.util.Log.d("SmsListenerService", "SIM $index: subscriptionId=$subscriptionId, phone_number=$phoneNumber, slotIndex=$slotIndex, iccId=$iccId")
            
            // Log warning if phone number is empty
            if (phoneNumber.isNullOrEmpty()) {
                android.util.Log.w("SmsListenerService", "Found SIM for subscriptionId $subscriptionId but phone_number is null/empty: '$phoneNumber'")
            }
        }
        
        // The SIM verification logic is implemented in the getSimNumber method
        // where we match the incoming SMS subscription ID with verified SIMs
        
        android.util.Log.d("SmsListenerService", "Service started with endpoint: $apiEndpoint, verified SIMs: ${verifiedSims.size}")
        return START_REDELIVER_INTENT
    }

    override fun onBind(intent: Intent?): IBinder? = null

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(CHANNEL_ID, "SMS Listener Service", NotificationManager.IMPORTANCE_HIGH).apply {
                description = "Background service for SMS monitoring"
                setShowBadge(true)
                enableVibration(true)
                enableLights(true)
            }
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): android.app.Notification {
        val notificationIntent = packageManager.getLaunchIntentForPackage(packageName)
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            notificationIntent,
            PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("SMS Sync Service")
            .setContentText("Monitoring SMS messages")
            .setSmallIcon(android.R.drawable.ic_dialog_email)
            .setPriority(NotificationCompat.PRIORITY_MAX)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setOngoing(true)
            .setForegroundServiceBehavior(NotificationCompat.FOREGROUND_SERVICE_IMMEDIATE)
            .setContentIntent(pendingIntent)
            .build()
    }

    private fun acquireWakeLock() {
        if (wakeLock == null) {
            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            wakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK,
                "SyncSMS::SmsServiceWakeLock"
            ).apply {
                acquire(10*60*1000L)
            }
        }
    }

    private fun releaseWakeLock() {
        wakeLock?.let {
            if (it.isHeld) {
                it.release()
            }
            wakeLock = null
        }
    }

    private val pendingMultipartMessages = mutableMapOf<String, MutableList<SmsMessage>>()

    private fun registerSmsReceiver() {
        smsReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                try {
                    if (intent?.action == Telephony.Sms.Intents.SMS_RECEIVED_ACTION) {
                        // Add MIUI-specific error handling
                        try {
                            // Check if we're running on MIUI
                            val isMIUI = Build.MANUFACTURER.equals("Xiaomi", ignoreCase = true)
                            if (isMIUI) {
                                android.util.Log.d("SmsListenerService", "Running on MIUI device")
                                // Add a delay to allow MIUI's security system to process the SMS
                                Thread.sleep(100)
                                try {
                                    // Explicitly clear any pending block type checks
                                    context?.contentResolver?.query(
                                        android.provider.Telephony.Sms.CONTENT_URI,
                                        null, null, null, null
                                    )?.close()
                                } catch (e: Exception) {
                                    android.util.Log.e("SmsListenerService", "Error clearing MIUI SMS block type: ${e.message}")
                                }
                            }
                        } catch (miuiError: Exception) {
                            android.util.Log.e("SmsListenerService", "MIUI security check error: ${miuiError.message}")
                            android.util.Log.e("SmsListenerService", "MIUI error stack trace: ${miuiError.stackTrace.joinToString("\n")}")
                            // Continue processing even if MIUI check fails
                        }

                        val messages = Telephony.Sms.Intents.getMessagesFromIntent(intent)
                        if (messages == null || messages.isEmpty()) return

                        // Check if this is a multi-part message by examining PDU count
                        if (messages.size > 1) {
                            handleMultipartMessage(messages, intent)
                        } else {
                            // Single message, process normally
                            // Try multiple possible keys for subscription ID
                            var subscriptionId = intent.getIntExtra("subscription", -1)
                            if (subscriptionId == -1) {
                                subscriptionId = intent.getIntExtra("subscription_id", -1)
                            }
                            if (subscriptionId == -1) {
                                subscriptionId = intent.getIntExtra("android.telephony.extra.SUBSCRIPTION_INDEX", -1)
                            }
                            processCompleteSmsMessage(messages[0], subscriptionId)
                        }
                    }
                } catch (e: Exception) {
                    android.util.Log.e("SmsListenerService", "Error processing SMS: ${e.message}")
                    android.util.Log.e("SmsListenerService", "Stack trace: ${e.stackTrace.joinToString("\n")}")
                }
            }
        }

        val intentFilter = IntentFilter(Telephony.Sms.Intents.SMS_RECEIVED_ACTION)
        intentFilter.priority = IntentFilter.SYSTEM_HIGH_PRIORITY
        val receiverFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Context.RECEIVER_EXPORTED
        } else {
            Context.RECEIVER_NOT_EXPORTED
        }
        registerReceiver(smsReceiver, intentFilter, receiverFlags)
    }

    private fun unregisterSmsReceiver() {
        smsReceiver?.let {
            unregisterReceiver(it)
            smsReceiver = null
        }
    }

    private fun sendEvent(eventName: String, params: Map<String, Any>) {
        android.util.Log.d("SmsListenerService", "Sending event: $eventName")
        try {
            // Send event through EventChannel instead of MethodChannel
            eventsSink?.success(mapOf(
                "event" to eventName,
                "data" to params
            ))
        } catch (e: Exception) {
            android.util.Log.e("SmsListenerService", "Error sending event: ${e.message}")
        }
    }

    private fun handleMultipartMessage(messages: Array<SmsMessage>, intent: Intent) {
        val firstMessage = messages[0]
        val messageId = "${firstMessage.displayOriginatingAddress}_${System.currentTimeMillis()}"
        
        synchronized(pendingMultipartMessages) {
            val parts = pendingMultipartMessages.getOrPut(messageId) { mutableListOf() }
            parts.addAll(messages)

            // Combine all message parts
            val combinedText = parts.joinToString("") { it.displayMessageBody }
            
            // Process the complete message using the first message's metadata
            processCompleteSmsMessage(firstMessage, intent.getIntExtra("subscription", -1), combinedText)
            
            // Remove processed message parts
            pendingMultipartMessages.remove(messageId)
        }
    }

    private fun isVerifiedSubscription(subscriptionId: Int): Boolean {
        return verifiedSims.any { sim ->
            val simSubscriptionId = (sim["subscriptionId"] as? Number)?.toInt()
            android.util.Log.d("SmsListenerService", "Sim ($simSubscriptionId) == ($subscriptionId)")
            simSubscriptionId?.equals(subscriptionId) ?: false
        }
    }

    private fun processCompleteSmsMessage(smsMessage: SmsMessage, subscriptionId: Int, overrideMessageBody: String? = null) {
        // Only process messages from verified SIM cards
        android.util.Log.d("SmsListenerService", "Verifying subscription - Current: $subscriptionId, Verified SIMs: $verifiedSims");
        val simNumber = getSimNumber(subscriptionId)
        if (!isVerifiedSubscription(subscriptionId) || simNumber == "unknown") {
            android.util.Log.d("SmsListenerService", "Ignoring SMS from unverified SIM (subscriptionId: $subscriptionId, simNumber: $simNumber)")
            return
        }

        android.util.Log.d("SmsListenerService", "Processing SMS message:")
        android.util.Log.d("SmsListenerService", "Sender: ${smsMessage.displayOriginatingAddress}")
        android.util.Log.d("SmsListenerService", "Message: ${overrideMessageBody ?: smsMessage.displayMessageBody}")
        android.util.Log.d("SmsListenerService", "Subscription ID: $subscriptionId")
        android.util.Log.d("SmsListenerService", "Timestamp: ${smsMessage.timestampMillis}")

        // Get SIM slot from subscription ID
        val simSlot = try {
            if (subscriptionId != -1) {
                val subscriptionManager = getSystemService(Context.TELEPHONY_SUBSCRIPTION_SERVICE) as SubscriptionManager
                val subscriptionInfo = subscriptionManager.getActiveSubscriptionInfo(subscriptionId)
                subscriptionInfo?.simSlotIndex ?: -1
            } else {
                -1
            }
        } catch (e: Exception) {
            android.util.Log.e("SmsListenerService", "Error getting SIM slot: ${e.message}")
            -1
        }
        
        android.util.Log.d("SmsListenerService", "Sending SMS data to Flutter - subscriptionId: $subscriptionId, simSlot: $simSlot")
        val messageData = mapOf(
            "sender" to smsMessage.displayOriginatingAddress,
            "message" to (overrideMessageBody ?: smsMessage.displayMessageBody),
            "timestamp" to smsMessage.timestampMillis,
            "subscriptionId" to subscriptionId,
            "simSlot" to simSlot
        )
        android.util.Log.d("SmsListenerService", "Message data: $messageData")
        sendEvent("onSmsReceived", messageData)
        sendToApi(smsMessage, subscriptionId, overrideMessageBody)
    }

    private fun sendToApi(smsMessage: SmsMessage, subscriptionId: Int = -1, overrideMessageBody: String? = null) {
        val endpoint = apiEndpoint ?: return
        val simNumber = getSimNumber(subscriptionId)
        if (simNumber == "unknown") {
            android.util.Log.d("SmsListenerService", "Not sending SMS to API because SIM number is unknown (subscriptionId: $subscriptionId)")
            return
        }

        android.util.Log.d("SmsListenerService", "Sending SMS to API:")
        android.util.Log.d("SmsListenerService", "Endpoint: $endpoint")
        android.util.Log.d("SmsListenerService", "SIM Number: $simNumber")
        android.util.Log.d("SmsListenerService", "Request body: ${smsMessage.displayMessageBody}")

        val jsonBody = JSONObject().apply {
            put("message", (overrideMessageBody ?: smsMessage.displayMessageBody).replace("\n", " "))
            put("payment_type", smsMessage.displayOriginatingAddress)
            put("receiver_number", simNumber)
        }

        android.util.Log.d("SmsListenerService", "Final request body: ${jsonBody.toString()}")

        val requestBody = jsonBody.toString().toRequestBody(JSON)
        val requestBuilder = Request.Builder()
            .url(endpoint)
            .post(requestBody)

        apiHeaders?.forEach { (key, value) ->
            requestBuilder.addHeader(key, value.toString())
        }
        
        val request = requestBuilder.build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                android.util.Log.e("SmsListenerService", "API call failed: ${e.message}")
                coroutineScope.launch {
                    savePendingMessage(smsMessage, subscriptionId)
                }
            }

            override fun onResponse(call: Call, response: Response) {
                response.use { 
                    if (!response.isSuccessful) {
                        android.util.Log.e("SmsListenerService", "API call unsuccessful: ${response.code} - ${response.message}")
                        coroutineScope.launch {
                            savePendingMessage(smsMessage, subscriptionId, overrideMessageBody)
                        }
                    } else {
                        android.util.Log.d("SmsListenerService", "API call successful: ${response.code}")
                    }
                }
            }
        })
    }

    private suspend fun savePendingMessage(smsMessage: SmsMessage, subscriptionId: Int = -1, overrideMessageBody: String? = null) {
        val simNumber = getSimNumber(subscriptionId)
        if (simNumber == "unknown") {
            android.util.Log.d("SmsListenerService", "Not saving message to database because SIM number is unknown")
            return
        }
        try {
            android.util.Log.d("SmsListenerService", "Attempting to save message to database")
            android.util.Log.d("SmsListenerService", "Message details - Sender: ${smsMessage.displayOriginatingAddress}, Timestamp: ${smsMessage.timestampMillis}")
            val pendingMessage = PendingSmsMessage(
                sender = smsMessage.displayOriginatingAddress,
                message = overrideMessageBody ?: smsMessage.displayMessageBody,
                timestamp = smsMessage.timestampMillis,
                paymentType = smsMessage.displayOriginatingAddress,
                receiverNumber = simNumber,
                retryCount = 0,
                lastRetryTimestamp = 0 // Set to 0 to ensure immediate retry attempt
            )
            pendingSmsMessageDao.insert(pendingMessage)
            android.util.Log.d("SmsListenerService", "Successfully saved message to database for later retry")
            // Trigger immediate processing if network is available
            if (isNetworkAvailable()) {
                android.util.Log.d("SmsListenerService", "Network available, triggering immediate message processing")
                processMessages()
            } else {
                android.util.Log.d("SmsListenerService", "Network unavailable, message will be processed when network is restored")
            }
        } catch (e: Exception) {
            android.util.Log.e("SmsListenerService", "Error saving message to database: ${e.message}")
            android.util.Log.e("SmsListenerService", "Stack trace: ${e.stackTrace.joinToString("\n")}")
        }
    }
}