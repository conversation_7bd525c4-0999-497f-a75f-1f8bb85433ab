package com.sbpay

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.provider.Telephony
import android.util.Log
import androidx.core.app.NotificationCompat

class SmsModule(private val context: Context) {
    private val CHANNEL_ID = "sms_service_channel"
    private val NOTIFICATION_ID = 1
    private val PREFS_NAME = "SmsModulePrefs"
    private val KEY_SERVICE_STATE = "service_state"
    private var isListening = false
    private var apiEndpoint: String? = null
    private var apiHeaders: Map<String, String>? = null
    private var verifiedSims: List<Any>? = null

    init {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        isListening = prefs.getBoolean(KEY_SERVICE_STATE, false)
    }

    fun getName() = "SmsModule"

    // Removed @ReactMethod annotation - will use MethodChannel
    fun startSmsListener(endpoint: String, headers: Map<String, Any>, verifiedSims: List<Any>) {
        try {
            Log.d("SmsModule", "=== Starting SMS Listener Service ===")
            Log.d("SmsModule", "API Endpoint: $endpoint")
            Log.d("SmsModule", "Headers: $headers")
            Log.d("SmsModule", "Verified SIMs: $verifiedSims")

            if (isListening) {
                Log.d("SmsModule", "SMS listener is already running - skipping startup")
                return
            }

            apiEndpoint = endpoint
            // Convert headers map to a simpler format for storage
            val headersMap = headers.mapValues { it.value.toString() }
            apiHeaders = null // Not used in Flutter version
            this.verifiedSims = verifiedSims
            
            // Convert verifiedSims to JSON string for storage
            // In Flutter version, verifiedSims is already a JSON string, so we just use it directly
            val verifiedSimsJson = try {
                if (verifiedSims.isNotEmpty()) {
                    // Convert List<Any> to proper JSON array
                    val jsonArray = org.json.JSONArray()
                    verifiedSims.forEach { sim ->
                        // If sim is already a JSON object, add it directly
                        // Otherwise, convert it to a JSON object
                        when (sim) {
                            is org.json.JSONObject -> jsonArray.put(sim)
                            is Map<*, *> -> {
                                val jsonObject = org.json.JSONObject()
                                sim.forEach { (key, value) ->
                                    jsonObject.put(key.toString(), value)
                                }
                                jsonArray.put(jsonObject)
                            }
                            else -> jsonArray.put(sim.toString())
                        }
                    }
                    jsonArray.toString()
                } else {
                    "[]"
                }
            } catch (e: Exception) {
                Log.e("SmsModule", "Error converting verified SIMs to JSON: ${e.message}")
                "[]"
            }

            // Save configuration for auto-restart after reboot
            Log.d("SmsModule", "Saving service configuration for auto-restart")
            BootCompletedReceiver.saveServiceConfig(
                context,
                endpoint,
                headersMap,
                verifiedSimsJson
            )
            
            // Also save the current SIM state for comparison after reboot
            try {
                val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                // We don't need to save the current SIM state here as it's already being saved by the SimStateMonitorService
                // Just ensure the service state is properly marked as running
                Log.d("SmsModule", "Service state marked as running for auto-restart")
            } catch (e: Exception) {
                Log.e("SmsModule", "Error saving service state: ${e.message}")
            }

            Log.d("SmsModule", "Creating notification channel")
            createNotificationChannel()
            Log.d("SmsModule", "Starting foreground service")
            startForegroundService()
            isListening = true
            
            // Save the service state
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit().putBoolean(KEY_SERVICE_STATE, true).apply()
            Log.d("SmsModule", "SMS listener started successfully")
        } catch (e: Exception) {
            Log.e("SmsModule", "Error starting SMS listener: ${e.message}")
            Log.e("SmsModule", "Stack trace: ${e.stackTrace.joinToString("\n")}")
        }
    }

    fun stopSmsListener() {
        try {
            Log.d("SmsModule", "Attempting to stop SMS listener")
            if (!isListening) {
                Log.d("SmsModule", "SMS listener is not running")
                return
            }

            Log.d("SmsModule", "Stopping foreground service")
            stopForegroundService()
            isListening = false
            apiEndpoint = null
            apiHeaders = null
            verifiedSims = null
            
            // Save the service state
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit().putBoolean(KEY_SERVICE_STATE, false).apply()
            Log.d("SmsModule", "SMS listener stopped successfully")
        } catch (e: Exception) {
            Log.e("SmsModule", "Error stopping SMS listener: ${e.message}")
        }
    }

    fun isListenerRunning(): Boolean {
        Log.d("SmsModule", "Checking listener status")
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
        val runningServices = manager.getRunningServices(Integer.MAX_VALUE)
        val isServiceRunning = runningServices?.any { 
            it.service.className == SmsListenerService::class.java.name 
        } ?: false
        Log.d("SmsModule", "Service running status: $isServiceRunning")
        isListening = isServiceRunning
        return isServiceRunning
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Log.d("SmsModule", "Creating notification channel for Android O and above")
            val name = "SMS Listener Service"
            val descriptionText = "Background service for SMS monitoring"
            val importance = NotificationManager.IMPORTANCE_LOW
            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
            }
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
            Log.d("SmsModule", "Notification channel created successfully")
        }
    }

    private fun startForegroundService() {
        val serviceIntent = Intent(context, SmsListenerService::class.java).apply {
            addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES)
            putExtra("apiEndpoint", apiEndpoint)
            // apiHeaders and verifiedSims are no longer used in Flutter version
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(serviceIntent)
        } else {
            context.startService(serviceIntent)
        }
    }

    private fun stopForegroundService() {
        val serviceIntent = Intent(context, SmsListenerService::class.java)
        serviceIntent.action = "STOP_SERVICE"
        context.stopService(serviceIntent)
    }
}