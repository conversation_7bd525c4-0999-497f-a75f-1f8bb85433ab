import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:com.sbpay/core/config/api_config.dart';
import '../../../../providers/auth_provider.dart';
import '../../../../providers/sim_provider.dart';
import '../../../../providers/sms_provider.dart';
import '../../../../providers/permission_provider.dart';
import '../../../../models/sim_card.dart';
import '../widgets/permission_card.dart';
import '../widgets/sim_status_card.dart';
import '../widgets/sms_status_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeScreen();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _checkPermissions();
      _checkSimVerification();
    }
  }

  Future<void> _initializeScreen() async {
    await _checkPermissions();
    await _checkSimVerification();
    await _checkListenerStatus();
  }

  Future<void> _checkPermissions() async {
    if (!mounted) return;
    final permissionProvider = context.read<PermissionProvider>();
    await permissionProvider.checkAllPermissions();
  }

  Future<void> _checkSimVerification() async {
    if (!mounted) return;
    final simProvider = context.read<SimProvider>();
    await simProvider.getSimInfo();
  }

  Future<void> _checkListenerStatus() async {
    if (!mounted) return;
    final smsProvider = context.read<SmsProvider>();
    await smsProvider.checkListenerStatus();
  }

  Future<void> _handleSignOut() async {
    try {
      // Stop SMS listener service if it's active
      final smsProvider = context.read<SmsProvider>();
      if (smsProvider.isListening) {
        await smsProvider.stopSmsListener();
      }

      // Clear all local data from all providers
      await context.read<AuthProvider>().clearAllLocalData();
      await context.read<SmsProvider>().clearAllMessages();
      await context.read<SimProvider>().clearAllSimData();
      await context.read<SmsProvider>().clearAllVeriableData();

      // Also clear the user data specifically
      await context.read<AuthProvider>().clearUser();

      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/login');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error signing out: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _toggleSmsListener() async {
    final smsProvider = context.read<SmsProvider>();
    final permissionProvider = context.read<PermissionProvider>();

    // Check permissions first
    if (!permissionProvider.allPermissionsGranted) {
      _showPermissionDialog();
      return;
    }

    try {
      if (smsProvider.isListening) {
        await smsProvider.stopSmsListener();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('SMS listener stopped'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } else {
        // Check for unverified SIMs
        final simProvider = context.read<SimProvider>();
        final authProvider = context.read<AuthProvider>();
        final activeSims = simProvider.activeSimCards;
        final verifiedSims = authProvider.verifiedSims;

        debugPrint('Active SIMs: ${activeSims.length}');
        debugPrint('Verified SIMs: ${verifiedSims.length}');

        // Check if at least one active SIM has been verified
        bool atLeastOneSimVerified = false;
        List<SimCardInfo> verifiedActiveSims = [];

        // For each active SIM, check if it's verified
        for (var activeSim in activeSims) {
          for (var verifiedSim in verifiedSims) {
            // Check if this active SIM matches a verified SIM
            if (verifiedSim.subscriptionId == activeSim.subscriptionId &&
                verifiedSim.iccId == activeSim.iccId &&
                verifiedSim.slotIndex == activeSim.slotIndex &&
                verifiedSim.isVerified) {
              atLeastOneSimVerified = true;
              verifiedActiveSims.add(activeSim);
              break;
            }
          }
        }

        debugPrint('At least one SIM verified: $atLeastOneSimVerified');
        debugPrint('Verified active SIMs: ${verifiedActiveSims.length}');

        // Check if we have at least one verified SIM to start monitoring
        if (!atLeastOneSimVerified || verifiedActiveSims.isEmpty) {
          // Show warning dialog - no SIMs verified
          if (mounted) {
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('No Verified SIM'),
                content: const Text(
                  'At least one SIM card must be verified before starting SMS listener.',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('OK'),
                  ),
                ],
              ),
            );
          }
          return;
        }

        // Get the auth token and endpoint
        final userAuthProvider = context.read<AuthProvider>();
        final String? token = userAuthProvider.user?.token;
        final String endpoint = '${ApiConfig.apiDomain}/api/transaction/verify';

        /// Test Endpoint
        // final String endpoint =
        //     'https://play.svix.com/in/e_MKMvCFRFjVZqN1a2KgOtrcdra3w/';

        // Get verified SIMs - only the ones that are actually verified and active
        final List<Map<String, dynamic>> verifiedSimData = verifiedActiveSims
            .map(
              (sim) => {
                'subscriptionId': sim.subscriptionId,
                'phone_number':
                    sim.phoneNumber, // Use phone number from SIM info
                'iccId': sim.iccId,
                'slotIndex': sim.slotIndex,
              },
            )
            .toList();

        debugPrint(
          'Starting SMS listener with ${verifiedSimData.length} verified SIMs',
        );

        final success = await smsProvider.startSmsListener(
          endpoint: endpoint,
          token: token,
          verifiedSims: verifiedSimData,
        );
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                success
                    ? 'SMS listener started successfully'
                    : 'Failed to start SMS listener',
              ),
              backgroundColor: success ? Colors.green : Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Permissions Required'),
        content: const Text(
          'All permissions must be granted before starting the SMS listener. Please grant the required permissions.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<PermissionProvider>().requestAllPermissions(
                context: context,
              );
            },
            child: const Text('Grant Permissions'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sbkash Home'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _initializeScreen,
          ),

          // PopupMenuButton<String>(
          //   onSelected: (value) {
          //     if (value == 'signout') {
          //       _showSignOutDialog();
          //     }
          //   },
          //   itemBuilder: (context) => [
          //     const PopupMenuItem(
          //       value: 'signout',
          //       child: Row(
          //         children: [
          //           Icon(Icons.logout, color: Colors.red),
          //           SizedBox(width: 8),
          //           Text('Sign Out'),
          //         ],
          //       ),
          //     ),
          //   ],
          // ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _initializeScreen,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 16),
              Align(
                alignment: Alignment.center,
                child: Text(
                  'SMS Transaction Monitor',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Permissions Section
              const PermissionCard(),
              const SizedBox(height: 16),

              // SIM Status Section
              const SimStatusCard(),
              const SizedBox(height: 16),

              // SMS Listener Section
              const SmsStatusCard(),
              const SizedBox(height: 16),

              // SMS Listener Control Button
              Consumer2<SmsProvider, PermissionProvider>(
                builder: (context, smsProvider, permissionProvider, _) {
                  final canStart = permissionProvider.allPermissionsGranted;
                  final isListening = smsProvider.isListening;
                  final isLoading = smsProvider.isLoading;

                  return ElevatedButton(
                    onPressed: isLoading ? null : _toggleSmsListener,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isListening ? Colors.red : Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : Text(
                            isListening
                                ? 'Stop SMS Monitor'
                                : 'Start SMS Monitor',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  );
                },
              ),

              const SizedBox(height: 16),

              /// Sign out button
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.red,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: const BorderSide(color: Colors.red, width: 1.5),
                  ),
                ),
                onPressed: _showSignOutDialog,
                child: const Text('Sign Out'),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  void _showSignOutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _handleSignOut();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }
}
